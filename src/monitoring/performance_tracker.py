#!/usr/bin/env python3
"""
Performance Tracking System for MT5 Trading Bot
Tracks and analyzes trading performance metrics
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np
from collections import defaultdict, deque
import threading
import time

logger = logging.getLogger(__name__)


@dataclass
class TradeRecord:
    """Data class for trade records"""
    ticket: int
    symbol: str
    type: str  # BUY, SELL
    volume: float
    open_price: float
    close_price: Optional[float] = None
    open_time: Optional[datetime] = None
    close_time: Optional[datetime] = None
    profit: Optional[float] = None
    commission: float = 0.0
    swap: float = 0.0
    strategy: Optional[str] = None
    magic_number: Optional[int] = None
    comment: Optional[str] = None

    @property
    def is_closed(self) -> bool:
        """Check if trade is closed"""
        return self.close_price is not None and self.close_time is not None

    @property
    def duration(self) -> Optional[timedelta]:
        """Get trade duration"""
        if self.open_time and self.close_time:
            return self.close_time - self.open_time
        return None

    @property
    def pips(self) -> Optional[float]:
        """Calculate pips for the trade"""
        if not self.is_closed:
            return None

        pip_value = 0.0001 if 'JPY' not in self.symbol else 0.01
        price_diff = self.close_price - self.open_price

        if self.type == 'SELL':
            price_diff = -price_diff

        return price_diff / pip_value


@dataclass
class PerformanceMetrics:
    """Data class for performance metrics"""
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_profit: float = 0.0
    total_loss: float = 0.0
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    net_profit: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    average_win: float = 0.0
    average_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    max_consecutive_wins: int = 0
    max_consecutive_losses: int = 0
    max_drawdown: float = 0.0
    max_drawdown_percent: float = 0.0
    recovery_factor: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    total_pips: float = 0.0
    average_pips_per_trade: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


class PerformanceTracker:
    """Tracks and analyzes trading performance"""

    def __init__(self, initial_balance: float = 10000.0):
        """
        Initialize performance tracker

        Args:
            initial_balance: Initial account balance
        """
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.trades: List[TradeRecord] = []
        self.open_trades: Dict[int, TradeRecord] = {}
        self.equity_curve: List[Tuple[datetime, float]] = []
        self.drawdown_curve: List[Tuple[datetime, float]] = []

        # Performance metrics by period
        self.daily_metrics: Dict[str, PerformanceMetrics] = {}
        self.weekly_metrics: Dict[str, PerformanceMetrics] = {}
        self.monthly_metrics: Dict[str, PerformanceMetrics] = {}

        # Real-time tracking
        self.peak_balance = initial_balance
        self.current_drawdown = 0.0
        self.max_drawdown = 0.0
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        self.max_consecutive_wins = 0
        self.max_consecutive_losses = 0

        # Thread safety
        self.lock = threading.Lock()

        # Recent performance buffer
        self.recent_trades = deque(maxlen=100)
        self.recent_returns = deque(maxlen=252)  # ~1 year of daily returns

    def add_trade(self, trade: TradeRecord) -> None:
        """
        Add a new trade record

        Args:
            trade: Trade record to add
        """
        with self.lock:
            if trade.is_closed:
                # Closed trade
                self.trades.append(trade)
                self.recent_trades.append(trade)

                # Update balance
                if trade.profit is not None:
                    self.current_balance += trade.profit

                # Update equity curve
                self.equity_curve.append((trade.close_time or datetime.now(), self.current_balance))

                # Update drawdown tracking
                self._update_drawdown()

                # Update consecutive wins/losses
                self._update_consecutive_stats(trade)

                # Update period metrics
                self._update_period_metrics(trade)

                logger.info(f"Trade added: {trade.symbol} {trade.type} {trade.volume} lots, "
                           f"Profit: {trade.profit:.2f}")

            else:
                # Open trade
                self.open_trades[trade.ticket] = trade

    def update_open_trade(self, ticket: int, current_price: float, current_profit: float) -> None:
        """
        Update open trade with current market data

        Args:
            ticket: Trade ticket
            current_price: Current market price
            current_profit: Current unrealized profit
        """
        with self.lock:
            if ticket in self.open_trades:
                trade = self.open_trades[ticket]
                # Update current profit for unrealized P&L tracking
                trade.profit = current_profit

    def close_trade(self, ticket: int, close_price: float, close_time: datetime,
                   final_profit: float) -> None:
        """
        Close an open trade

        Args:
            ticket: Trade ticket
            close_price: Closing price
            close_time: Closing time
            final_profit: Final realized profit
        """
        with self.lock:
            if ticket in self.open_trades:
                trade = self.open_trades.pop(ticket)
                trade.close_price = close_price
                trade.close_time = close_time
                trade.profit = final_profit

                # Add to closed trades
                self.add_trade(trade)

    def get_current_metrics(self) -> PerformanceMetrics:
        """
        Get current overall performance metrics

        Returns:
            Current performance metrics
        """
        with self.lock:
            return self._calculate_metrics(self.trades)

    def get_period_metrics(self, period: str, date_key: str) -> Optional[PerformanceMetrics]:
        """
        Get performance metrics for specific period

        Args:
            period: Period type ('daily', 'weekly', 'monthly')
            date_key: Date key (e.g., '2023-12-01' for daily)

        Returns:
            Performance metrics for the period or None if not found
        """
        with self.lock:
            if period == 'daily':
                return self.daily_metrics.get(date_key)
            elif period == 'weekly':
                return self.weekly_metrics.get(date_key)
            elif period == 'monthly':
                return self.monthly_metrics.get(date_key)
            return None

    def get_equity_curve(self) -> List[Tuple[datetime, float]]:
        """Get equity curve data"""
        with self.lock:
            return self.equity_curve.copy()

    def get_drawdown_curve(self) -> List[Tuple[datetime, float]]:
        """Get drawdown curve data"""
        with self.lock:
            return self.drawdown_curve.copy()

    def get_trade_history(self, symbol: Optional[str] = None,
                         strategy: Optional[str] = None,
                         limit: Optional[int] = None) -> List[TradeRecord]:
        """
        Get trade history with optional filters

        Args:
            symbol: Filter by symbol
            strategy: Filter by strategy
            limit: Limit number of trades returned

        Returns:
            Filtered list of trade records
        """
        with self.lock:
            trades = self.trades.copy()

            # Apply filters
            if symbol:
                trades = [t for t in trades if t.symbol == symbol]
            if strategy:
                trades = [t for t in trades if t.strategy == strategy]

            # Sort by close time (most recent first)
            trades.sort(key=lambda t: t.close_time or datetime.min, reverse=True)

            # Apply limit
            if limit:
                trades = trades[:limit]

            return trades

    def get_symbol_performance(self) -> Dict[str, PerformanceMetrics]:
        """
        Get performance metrics by symbol

        Returns:
            Dictionary of symbol -> performance metrics
        """
        with self.lock:
            symbol_trades = defaultdict(list)

            for trade in self.trades:
                symbol_trades[trade.symbol].append(trade)

            symbol_metrics = {}
            for symbol, trades in symbol_trades.items():
                symbol_metrics[symbol] = self._calculate_metrics(trades)

            return symbol_metrics

    def get_strategy_performance(self) -> Dict[str, PerformanceMetrics]:
        """
        Get performance metrics by strategy

        Returns:
            Dictionary of strategy -> performance metrics
        """
        with self.lock:
            strategy_trades = defaultdict(list)

            for trade in self.trades:
                if trade.strategy:
                    strategy_trades[trade.strategy].append(trade)

            strategy_metrics = {}
            for strategy, trades in strategy_trades.items():
                strategy_metrics[strategy] = self._calculate_metrics(trades)

            return strategy_metrics

    def get_recent_performance(self, days: int = 30) -> PerformanceMetrics:
        """
        Get performance metrics for recent period

        Args:
            days: Number of recent days to analyze

        Returns:
            Performance metrics for recent period
        """
        with self.lock:
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_trades = [
                t for t in self.trades
                if t.close_time and t.close_time >= cutoff_date
            ]
            return self._calculate_metrics(recent_trades)

    def reset_metrics(self) -> None:
        """Reset all performance metrics"""
        with self.lock:
            self.trades.clear()
            self.open_trades.clear()
            self.equity_curve.clear()
            self.drawdown_curve.clear()
            self.daily_metrics.clear()
            self.weekly_metrics.clear()
            self.monthly_metrics.clear()

            self.current_balance = self.initial_balance
            self.peak_balance = self.initial_balance
            self.current_drawdown = 0.0
            self.max_drawdown = 0.0
            self.consecutive_wins = 0
            self.consecutive_losses = 0
            self.max_consecutive_wins = 0
            self.max_consecutive_losses = 0

            self.recent_trades.clear()
            self.recent_returns.clear()

            logger.info("Performance metrics reset")

    def _update_drawdown(self) -> None:
        """Update drawdown tracking"""
        if self.current_balance > self.peak_balance:
            self.peak_balance = self.current_balance
            self.current_drawdown = 0.0
        else:
            self.current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
            if self.current_drawdown > self.max_drawdown:
                self.max_drawdown = self.current_drawdown

        # Add to drawdown curve
        self.drawdown_curve.append((datetime.now(), self.current_drawdown))

    def _update_consecutive_stats(self, trade: TradeRecord) -> None:
        """Update consecutive wins/losses statistics"""
        if trade.profit and trade.profit > 0:
            self.consecutive_wins += 1
            self.consecutive_losses = 0
            if self.consecutive_wins > self.max_consecutive_wins:
                self.max_consecutive_wins = self.consecutive_wins
        elif trade.profit and trade.profit < 0:
            self.consecutive_losses += 1
            self.consecutive_wins = 0
            if self.consecutive_losses > self.max_consecutive_losses:
                self.max_consecutive_losses = self.consecutive_losses

    def _update_period_metrics(self, trade: TradeRecord) -> None:
        """Update period-based metrics"""
        if not trade.close_time:
            return

        # Daily metrics
        daily_key = trade.close_time.strftime('%Y-%m-%d')
        if daily_key not in self.daily_metrics:
            self.daily_metrics[daily_key] = PerformanceMetrics()

        # Weekly metrics (ISO week)
        weekly_key = trade.close_time.strftime('%Y-W%U')
        if weekly_key not in self.weekly_metrics:
            self.weekly_metrics[weekly_key] = PerformanceMetrics()

        # Monthly metrics
        monthly_key = trade.close_time.strftime('%Y-%m')
        if monthly_key not in self.monthly_metrics:
            self.monthly_metrics[monthly_key] = PerformanceMetrics()

        # Update each period
        for period_metrics in [self.daily_metrics[daily_key],
                              self.weekly_metrics[weekly_key],
                              self.monthly_metrics[monthly_key]]:
            self._update_metrics_with_trade(period_metrics, trade)

    def _update_metrics_with_trade(self, metrics: PerformanceMetrics, trade: TradeRecord) -> None:
        """Update metrics object with new trade"""
        metrics.total_trades += 1

        if trade.profit and trade.profit > 0:
            metrics.winning_trades += 1
            metrics.gross_profit += trade.profit
            if trade.profit > metrics.largest_win:
                metrics.largest_win = trade.profit
        elif trade.profit and trade.profit < 0:
            metrics.losing_trades += 1
            metrics.gross_loss += abs(trade.profit)
            if abs(trade.profit) > metrics.largest_loss:
                metrics.largest_loss = abs(trade.profit)

        if trade.profit:
            metrics.net_profit += trade.profit

        if trade.pips:
            metrics.total_pips += trade.pips

        # Recalculate derived metrics
        self._recalculate_derived_metrics(metrics)

    def _calculate_metrics(self, trades: List[TradeRecord]) -> PerformanceMetrics:
        """
        Calculate performance metrics for a list of trades

        Args:
            trades: List of trade records

        Returns:
            Calculated performance metrics
        """
        if not trades:
            return PerformanceMetrics()

        metrics = PerformanceMetrics()
        profits = []
        losses = []
        all_profits = []
        pips_list = []

        for trade in trades:
            metrics.total_trades += 1

            if trade.profit is not None:
                all_profits.append(trade.profit)

                if trade.profit > 0:
                    metrics.winning_trades += 1
                    metrics.gross_profit += trade.profit
                    profits.append(trade.profit)
                    if trade.profit > metrics.largest_win:
                        metrics.largest_win = trade.profit
                elif trade.profit < 0:
                    metrics.losing_trades += 1
                    loss_amount = abs(trade.profit)
                    metrics.gross_loss += loss_amount
                    losses.append(loss_amount)
                    if loss_amount > metrics.largest_loss:
                        metrics.largest_loss = loss_amount

                metrics.net_profit += trade.profit

            if trade.pips is not None:
                pips_list.append(trade.pips)
                metrics.total_pips += trade.pips

        # Calculate derived metrics
        if metrics.total_trades > 0:
            metrics.win_rate = metrics.winning_trades / metrics.total_trades

        if profits:
            metrics.average_win = sum(profits) / len(profits)

        if losses:
            metrics.average_loss = sum(losses) / len(losses)

        if metrics.gross_loss > 0:
            metrics.profit_factor = metrics.gross_profit / metrics.gross_loss

        if pips_list:
            metrics.average_pips_per_trade = sum(pips_list) / len(pips_list)

        # Calculate risk-adjusted metrics
        if all_profits:
            returns_array = np.array(all_profits)

            # Sharpe ratio (assuming risk-free rate = 0)
            if np.std(returns_array) > 0:
                metrics.sharpe_ratio = np.mean(returns_array) / np.std(returns_array)

            # Sortino ratio (downside deviation)
            negative_returns = returns_array[returns_array < 0]
            if len(negative_returns) > 0 and np.std(negative_returns) > 0:
                metrics.sortino_ratio = np.mean(returns_array) / np.std(negative_returns)

        return metrics

    def _recalculate_derived_metrics(self, metrics: PerformanceMetrics) -> None:
        """Recalculate derived metrics for a metrics object"""
        if metrics.total_trades > 0:
            metrics.win_rate = metrics.winning_trades / metrics.total_trades

        if metrics.winning_trades > 0:
            metrics.average_win = metrics.gross_profit / metrics.winning_trades

        if metrics.losing_trades > 0:
            metrics.average_loss = metrics.gross_loss / metrics.losing_trades

        if metrics.gross_loss > 0:
            metrics.profit_factor = metrics.gross_profit / metrics.gross_loss

        if metrics.total_trades > 0 and metrics.total_pips != 0:
            metrics.average_pips_per_trade = metrics.total_pips / metrics.total_trades
