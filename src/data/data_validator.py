#!/usr/bin/env python3
"""
Data Validator for MT5 Trading Bot
Validates market data integrity and quality
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class DataValidator:
    """Validates market data quality and integrity"""
    
    def __init__(self):
        """Initialize Data Validator"""
        self.validation_rules = {
            'max_gap_minutes': 60,  # Maximum gap between bars in minutes
            'max_price_change_pct': 10.0,  # Maximum price change percentage
            'min_volume': 0,  # Minimum volume
            'max_spread_pct': 1.0,  # Maximum spread as percentage of price
            'ohlc_consistency': True,  # Check OHLC consistency
            'duplicate_timestamps': True,  # Check for duplicate timestamps
            'future_timestamps': True,  # Check for future timestamps
            'negative_prices': True,  # Check for negative prices
            'zero_prices': True,  # Check for zero prices
        }
        
    def validate_ohlcv_data(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """
        Validate OHLCV DataFrame
        
        Args:
            df: OHLCV DataFrame to validate
            symbol: Symbol name for logging
            
        Returns:
            True if data is valid, False otherwise
        """
        if df is None or df.empty:
            logger.warning(f"Empty data for {symbol}")
            return False
            
        try:
            # Check required columns
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"Missing columns for {symbol}: {missing_columns}")
                return False
                
            # Run validation checks
            validation_results = []
            
            validation_results.append(self._check_ohlc_consistency(df, symbol))
            validation_results.append(self._check_price_validity(df, symbol))
            validation_results.append(self._check_volume_validity(df, symbol))
            validation_results.append(self._check_timestamp_validity(df, symbol))
            validation_results.append(self._check_price_gaps(df, symbol))
            validation_results.append(self._check_duplicates(df, symbol))
            
            # All checks must pass
            is_valid = all(validation_results)
            
            if is_valid:
                logger.debug(f"Data validation passed for {symbol}")
            else:
                logger.warning(f"Data validation failed for {symbol}")
                
            return is_valid
            
        except Exception as e:
            logger.error(f"Error validating data for {symbol}: {e}")
            return False
            
    def validate_tick_data(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """
        Validate tick DataFrame
        
        Args:
            df: Tick DataFrame to validate
            symbol: Symbol name for logging
            
        Returns:
            True if data is valid, False otherwise
        """
        if df is None or df.empty:
            logger.warning(f"Empty tick data for {symbol}")
            return False
            
        try:
            # Check required columns
            required_columns = ['bid', 'ask', 'last']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"Missing tick columns for {symbol}: {missing_columns}")
                return False
                
            # Check bid/ask validity
            if not self._check_bid_ask_validity(df, symbol):
                return False
                
            # Check for negative prices
            if (df[['bid', 'ask', 'last']] <= 0).any().any():
                logger.error(f"Negative or zero prices found in tick data for {symbol}")
                return False
                
            # Check timestamp validity
            if not self._check_timestamp_validity(df, symbol):
                return False
                
            logger.debug(f"Tick data validation passed for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Error validating tick data for {symbol}: {e}")
            return False
            
    def get_data_quality_score(self, df: pd.DataFrame, symbol: str = None) -> float:
        """
        Calculate data quality score (0-100)
        
        Args:
            df: DataFrame to analyze
            symbol: Symbol name for logging
            
        Returns:
            Quality score from 0 to 100
        """
        if df is None or df.empty:
            return 0.0
            
        try:
            score = 100.0
            penalties = []
            
            # Check for missing data
            missing_pct = df.isnull().sum().sum() / (len(df) * len(df.columns)) * 100
            if missing_pct > 0:
                penalty = min(missing_pct * 2, 20)  # Max 20 point penalty
                penalties.append(('missing_data', penalty))
                
            # Check for price gaps
            if 'Close' in df.columns:
                price_changes = df['Close'].pct_change().abs()
                large_gaps = (price_changes > 0.05).sum()  # 5% threshold
                if large_gaps > 0:
                    penalty = min(large_gaps * 2, 15)  # Max 15 point penalty
                    penalties.append(('price_gaps', penalty))
                    
            # Check for volume anomalies
            if 'Volume' in df.columns:
                zero_volume = (df['Volume'] == 0).sum()
                if zero_volume > 0:
                    penalty = min((zero_volume / len(df)) * 10, 10)  # Max 10 point penalty
                    penalties.append(('zero_volume', penalty))
                    
            # Check timestamp consistency
            if isinstance(df.index, pd.DatetimeIndex):
                time_diffs = df.index.to_series().diff()
                irregular_intervals = (time_diffs.dt.total_seconds() <= 0).sum()
                if irregular_intervals > 0:
                    penalty = min(irregular_intervals * 5, 15)  # Max 15 point penalty
                    penalties.append(('irregular_timestamps', penalty))
                    
            # Apply penalties
            total_penalty = sum(penalty for _, penalty in penalties)
            score = max(0.0, score - total_penalty)
            
            if penalties:
                logger.debug(f"Data quality penalties for {symbol}: {penalties}")
                
            return score
            
        except Exception as e:
            logger.error(f"Error calculating data quality score for {symbol}: {e}")
            return 0.0
            
    def detect_anomalies(self, df: pd.DataFrame, symbol: str = None) -> List[Dict[str, Any]]:
        """
        Detect anomalies in market data
        
        Args:
            df: DataFrame to analyze
            symbol: Symbol name for logging
            
        Returns:
            List of detected anomalies
        """
        anomalies = []
        
        if df is None or df.empty:
            return anomalies
            
        try:
            # Price spike detection
            if 'Close' in df.columns:
                price_changes = df['Close'].pct_change().abs()
                spikes = price_changes > 0.1  # 10% threshold
                
                for idx in df[spikes].index:
                    anomalies.append({
                        'type': 'price_spike',
                        'timestamp': idx,
                        'value': price_changes.loc[idx],
                        'description': f'Price spike of {price_changes.loc[idx]:.2%}'
                    })
                    
            # Volume anomalies
            if 'Volume' in df.columns:
                volume_mean = df['Volume'].mean()
                volume_std = df['Volume'].std()
                
                # Detect unusually high volume
                high_volume_threshold = volume_mean + 3 * volume_std
                high_volume = df['Volume'] > high_volume_threshold
                
                for idx in df[high_volume].index:
                    anomalies.append({
                        'type': 'high_volume',
                        'timestamp': idx,
                        'value': df.loc[idx, 'Volume'],
                        'description': f'Unusually high volume: {df.loc[idx, "Volume"]}'
                    })
                    
                # Detect zero volume
                zero_volume = df['Volume'] == 0
                for idx in df[zero_volume].index:
                    anomalies.append({
                        'type': 'zero_volume',
                        'timestamp': idx,
                        'value': 0,
                        'description': 'Zero volume detected'
                    })
                    
            # Gap detection
            if isinstance(df.index, pd.DatetimeIndex) and len(df) > 1:
                time_diffs = df.index.to_series().diff()
                expected_interval = time_diffs.mode().iloc[0] if not time_diffs.mode().empty else timedelta(minutes=1)
                
                large_gaps = time_diffs > expected_interval * 3
                for idx in df[large_gaps].index:
                    anomalies.append({
                        'type': 'time_gap',
                        'timestamp': idx,
                        'value': time_diffs.loc[idx].total_seconds() / 60,
                        'description': f'Time gap of {time_diffs.loc[idx].total_seconds() / 60:.1f} minutes'
                    })
                    
            if anomalies:
                logger.info(f"Detected {len(anomalies)} anomalies in {symbol} data")
                
            return anomalies
            
        except Exception as e:
            logger.error(f"Error detecting anomalies for {symbol}: {e}")
            return anomalies
            
    def _check_ohlc_consistency(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """Check OHLC price consistency"""
        try:
            # High should be >= Open, Close, Low
            high_valid = (df['High'] >= df['Open']) & (df['High'] >= df['Close']) & (df['High'] >= df['Low'])
            
            # Low should be <= Open, Close, High
            low_valid = (df['Low'] <= df['Open']) & (df['Low'] <= df['Close']) & (df['Low'] <= df['High'])
            
            invalid_bars = ~(high_valid & low_valid)
            
            if invalid_bars.any():
                invalid_count = invalid_bars.sum()
                logger.error(f"OHLC consistency check failed for {symbol}: {invalid_count} invalid bars")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error checking OHLC consistency for {symbol}: {e}")
            return False
            
    def _check_price_validity(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """Check price validity (positive, non-zero)"""
        try:
            price_columns = ['Open', 'High', 'Low', 'Close']
            
            for col in price_columns:
                if col in df.columns:
                    # Check for negative or zero prices
                    if (df[col] <= 0).any():
                        logger.error(f"Invalid prices found in {col} for {symbol}")
                        return False
                        
                    # Check for extremely large price changes
                    if len(df) > 1:
                        price_changes = df[col].pct_change().abs()
                        large_changes = price_changes > self.validation_rules['max_price_change_pct'] / 100
                        
                        if large_changes.any():
                            logger.warning(f"Large price changes detected in {col} for {symbol}")
                            
            return True
            
        except Exception as e:
            logger.error(f"Error checking price validity for {symbol}: {e}")
            return False
            
    def _check_volume_validity(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """Check volume validity"""
        try:
            if 'Volume' not in df.columns:
                return True
                
            # Check for negative volume
            if (df['Volume'] < 0).any():
                logger.error(f"Negative volume found for {symbol}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error checking volume validity for {symbol}: {e}")
            return False
            
    def _check_timestamp_validity(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """Check timestamp validity"""
        try:
            if not isinstance(df.index, pd.DatetimeIndex):
                logger.warning(f"Index is not DatetimeIndex for {symbol}")
                return True  # Not critical
                
            # Check for future timestamps
            if self.validation_rules['future_timestamps']:
                future_timestamps = df.index > datetime.now()
                if future_timestamps.any():
                    logger.warning(f"Future timestamps found for {symbol}")
                    
            # Check for duplicate timestamps
            if self.validation_rules['duplicate_timestamps']:
                duplicates = df.index.duplicated()
                if duplicates.any():
                    logger.error(f"Duplicate timestamps found for {symbol}")
                    return False
                    
            return True
            
        except Exception as e:
            logger.error(f"Error checking timestamp validity for {symbol}: {e}")
            return False
            
    def _check_price_gaps(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """Check for unusual price gaps"""
        try:
            if 'Close' not in df.columns or len(df) < 2:
                return True
                
            price_changes = df['Close'].pct_change().abs()
            large_gaps = price_changes > self.validation_rules['max_price_change_pct'] / 100
            
            if large_gaps.any():
                gap_count = large_gaps.sum()
                logger.warning(f"Found {gap_count} large price gaps for {symbol}")
                
            return True  # Warning only, not critical
            
        except Exception as e:
            logger.error(f"Error checking price gaps for {symbol}: {e}")
            return False
            
    def _check_duplicates(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """Check for duplicate rows"""
        try:
            duplicates = df.duplicated()
            if duplicates.any():
                duplicate_count = duplicates.sum()
                logger.warning(f"Found {duplicate_count} duplicate rows for {symbol}")
                
            return True  # Warning only
            
        except Exception as e:
            logger.error(f"Error checking duplicates for {symbol}: {e}")
            return False
            
    def _check_bid_ask_validity(self, df: pd.DataFrame, symbol: str = None) -> bool:
        """Check bid/ask price validity"""
        try:
            # Bid should be less than or equal to ask
            if 'bid' in df.columns and 'ask' in df.columns:
                invalid_spread = df['bid'] > df['ask']
                if invalid_spread.any():
                    logger.error(f"Invalid bid/ask spread found for {symbol}")
                    return False
                    
            return True
            
        except Exception as e:
            logger.error(f"Error checking bid/ask validity for {symbol}: {e}")
            return False
