#!/usr/bin/env python3
"""
Data Handler for MT5 Trading Bot
Orchestrates market data operations, caching, and validation
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
import threading
import time
from pathlib import Path
import pickle
import json

from .market_data import MarketData, Timeframe, Tick, OHLCV
from .data_validator import DataValidator
from ..utils.config_manager import config_manager

logger = logging.getLogger(__name__)


class DataHandler:
    """Main data handler orchestrating all data operations"""

    def __init__(self, connection_manager, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Data Handler

        Args:
            connection_manager: MT5 connection manager
            config: Data configuration
        """
        self.connection_manager = connection_manager
        self.config = config or config_manager.get('data', {})

        # Initialize components
        self.market_data = MarketData(connection_manager)
        self.validator = DataValidator()

        # Caching configuration
        self.cache_enabled = self.config.get('cache_enabled', True)
        self.cache_dir = Path(self.config.get('cache_dir', 'data/cache'))
        self.cache_expiry_hours = self.config.get('cache_expiry_hours', 24)

        # Data buffers and cache
        self.data_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_lock = threading.Lock()

        # Subscriptions and callbacks
        self.data_subscribers: Dict[str, List[Callable]] = {}

        # Background tasks
        self.cache_cleanup_thread: Optional[threading.Thread] = None
        self.cleanup_active = False

        # Initialize cache directory
        if self.cache_enabled:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            self._start_cache_cleanup()

    def get_historical_data(self, symbol: str, timeframe: str, count: int = 1000,
                           start_time: Optional[datetime] = None,
                           use_cache: bool = True,
                           validate: bool = True) -> Optional[pd.DataFrame]:
        """
        Get historical data with caching and validation

        Args:
            symbol: Symbol name
            timeframe: Timeframe string (M1, M5, M15, etc.)
            count: Number of bars
            start_time: Start time (optional)
            use_cache: Whether to use cache
            validate: Whether to validate data

        Returns:
            DataFrame with historical data or None if failed
        """
        try:
            # Convert timeframe string to enum
            tf_enum = self._get_timeframe_enum(timeframe)
            if not tf_enum:
                logger.error(f"Invalid timeframe: {timeframe}")
                return None

            # Check cache first
            cache_key = f"{symbol}_{timeframe}_{count}_{start_time}"
            if use_cache and self.cache_enabled:
                cached_data = self._get_from_cache(cache_key)
                if cached_data is not None:
                    logger.debug(f"Retrieved {symbol} {timeframe} data from cache")
                    return cached_data

            # Get data from MT5
            df = self.market_data.get_historical_data(symbol, tf_enum, count, start_time)
            if df is None:
                return None

            # Validate data
            if validate and not self.validator.validate_ohlcv_data(df):
                logger.warning(f"Data validation failed for {symbol} {timeframe}")
                return None

            # Calculate indicators
            df = self.market_data.calculate_indicators(df)

            # Cache the data
            if use_cache and self.cache_enabled:
                self._save_to_cache(cache_key, df)

            logger.info(f"Retrieved {len(df)} bars for {symbol} {timeframe}")
            return df

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return None

    def get_current_price(self, symbol: str) -> Optional[Dict[str, float]]:
        """
        Get current price information

        Args:
            symbol: Symbol name

        Returns:
            Dictionary with current price info or None if failed
        """
        try:
            tick = self.market_data.get_current_tick(symbol)
            if not tick:
                return None

            return {
                'bid': tick.bid,
                'ask': tick.ask,
                'last': tick.last,
                'spread': tick.spread,
                'mid': tick.mid_price,
                'time': tick.time
            }

        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None

    def get_multiple_symbols_data(self, symbols: List[str], timeframe: str,
                                 count: int = 1000) -> Dict[str, pd.DataFrame]:
        """
        Get historical data for multiple symbols

        Args:
            symbols: List of symbol names
            timeframe: Timeframe string
            count: Number of bars

        Returns:
            Dictionary mapping symbol to DataFrame
        """
        results = {}

        for symbol in symbols:
            df = self.get_historical_data(symbol, timeframe, count)
            if df is not None:
                results[symbol] = df
            else:
                logger.warning(f"Failed to get data for {symbol}")

        return results

    def subscribe_to_price_updates(self, symbol: str, callback: Callable) -> bool:
        """
        Subscribe to real-time price updates

        Args:
            symbol: Symbol to subscribe to
            callback: Callback function for price updates

        Returns:
            True if subscription successful
        """
        try:
            # Create wrapper callback to format data
            def price_callback(tick: Tick):
                price_data = {
                    'symbol': tick.symbol,
                    'bid': tick.bid,
                    'ask': tick.ask,
                    'last': tick.last,
                    'spread': tick.spread,
                    'time': tick.time
                }
                callback(price_data)

            # Subscribe to market data
            success = self.market_data.subscribe_to_ticks(symbol, price_callback)

            if success:
                # Track subscription
                if symbol not in self.data_subscribers:
                    self.data_subscribers[symbol] = []
                self.data_subscribers[symbol].append(callback)

            return success

        except Exception as e:
            logger.error(f"Error subscribing to price updates for {symbol}: {e}")
            return False

    def unsubscribe_from_price_updates(self, symbol: str, callback: Callable) -> bool:
        """
        Unsubscribe from price updates

        Args:
            symbol: Symbol to unsubscribe from
            callback: Callback function to remove

        Returns:
            True if unsubscription successful
        """
        try:
            # Remove from subscribers
            if symbol in self.data_subscribers:
                if callback in self.data_subscribers[symbol]:
                    self.data_subscribers[symbol].remove(callback)

                # Clean up empty lists
                if not self.data_subscribers[symbol]:
                    del self.data_subscribers[symbol]

            return True

        except Exception as e:
            logger.error(f"Error unsubscribing from price updates for {symbol}: {e}")
            return False

    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get symbol information

        Args:
            symbol: Symbol name

        Returns:
            Symbol information dictionary
        """
        return self.market_data.get_symbol_info(symbol)

    def validate_symbol(self, symbol: str) -> bool:
        """
        Validate if symbol is tradeable

        Args:
            symbol: Symbol to validate

        Returns:
            True if symbol is valid
        """
        return self.market_data.validate_symbol(symbol)

    def get_available_symbols(self) -> List[str]:
        """
        Get list of available symbols

        Returns:
            List of available symbol names
        """
        # This would need to be implemented based on MT5 API
        # For now, return common forex pairs
        return [
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD',
            'USDCAD', 'NZDUSD', 'EURJPY', 'GBPJPY', 'EURGBP'
        ]

    def calculate_correlation(self, symbols: List[str], timeframe: str,
                            period: int = 100) -> Optional[pd.DataFrame]:
        """
        Calculate correlation matrix for symbols

        Args:
            symbols: List of symbols
            timeframe: Timeframe string
            period: Number of periods for correlation

        Returns:
            Correlation matrix DataFrame
        """
        try:
            # Get data for all symbols
            data_dict = self.get_multiple_symbols_data(symbols, timeframe, period)

            if len(data_dict) < 2:
                logger.warning("Need at least 2 symbols for correlation calculation")
                return None

            # Create DataFrame with close prices
            price_data = pd.DataFrame()
            for symbol, df in data_dict.items():
                if not df.empty:
                    price_data[symbol] = df['Close']

            if price_data.empty:
                return None

            # Calculate correlation
            correlation_matrix = price_data.corr()
            return correlation_matrix

        except Exception as e:
            logger.error(f"Error calculating correlation: {e}")
            return None

    def get_market_summary(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get market summary for multiple symbols

        Args:
            symbols: List of symbols

        Returns:
            Dictionary with market summary for each symbol
        """
        summary = {}

        for symbol in symbols:
            try:
                # Get current price
                price_info = self.get_current_price(symbol)

                # Get recent data for daily change calculation
                df = self.get_historical_data(symbol, 'D1', 2, use_cache=False)

                daily_change = 0.0
                daily_change_pct = 0.0

                if df is not None and len(df) >= 2:
                    current_close = df['Close'].iloc[-1]
                    previous_close = df['Close'].iloc[-2]
                    daily_change = current_close - previous_close
                    daily_change_pct = (daily_change / previous_close) * 100

                summary[symbol] = {
                    'current_price': price_info,
                    'daily_change': daily_change,
                    'daily_change_pct': daily_change_pct,
                    'is_market_open': self.market_data.is_market_open(symbol)
                }

            except Exception as e:
                logger.error(f"Error getting market summary for {symbol}: {e}")
                summary[symbol] = {'error': str(e)}

        return summary

    def _get_timeframe_enum(self, timeframe: str) -> Optional[Timeframe]:
        """
        Convert timeframe string to enum

        Args:
            timeframe: Timeframe string

        Returns:
            Timeframe enum or None if invalid
        """
        timeframe_map = {
            'M1': Timeframe.M1,
            'M5': Timeframe.M5,
            'M15': Timeframe.M15,
            'M30': Timeframe.M30,
            'H1': Timeframe.H1,
            'H4': Timeframe.H4,
            'D1': Timeframe.D1,
            'W1': Timeframe.W1,
            'MN1': Timeframe.MN1
        }
        return timeframe_map.get(timeframe.upper())

    def _get_cache_key(self, symbol: str, timeframe: str, count: int,
                      start_time: Optional[datetime] = None) -> str:
        """Generate cache key for data"""
        key_parts = [symbol, timeframe, str(count)]
        if start_time:
            key_parts.append(start_time.strftime('%Y%m%d_%H%M%S'))
        return '_'.join(key_parts)

    def _get_cache_file_path(self, cache_key: str) -> Path:
        """Get cache file path for key"""
        return self.cache_dir / f"{cache_key}.pkl"

    def _get_from_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """
        Get data from cache

        Args:
            cache_key: Cache key

        Returns:
            Cached DataFrame or None if not found/expired
        """
        try:
            cache_file = self._get_cache_file_path(cache_key)

            if not cache_file.exists():
                return None

            # Check if cache is expired
            file_age = datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)
            if file_age > timedelta(hours=self.cache_expiry_hours):
                cache_file.unlink()  # Delete expired cache
                return None

            # Load from cache
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)

            logger.debug(f"Loaded data from cache: {cache_key}")
            return data

        except Exception as e:
            logger.error(f"Error loading from cache {cache_key}: {e}")
            return None

    def _save_to_cache(self, cache_key: str, data: pd.DataFrame) -> bool:
        """
        Save data to cache

        Args:
            cache_key: Cache key
            data: DataFrame to cache

        Returns:
            True if saved successfully
        """
        try:
            cache_file = self._get_cache_file_path(cache_key)

            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)

            logger.debug(f"Saved data to cache: {cache_key}")
            return True

        except Exception as e:
            logger.error(f"Error saving to cache {cache_key}: {e}")
            return False

    def _start_cache_cleanup(self) -> None:
        """Start cache cleanup background task"""
        if self.cleanup_active:
            return

        self.cleanup_active = True
        self.cache_cleanup_thread = threading.Thread(
            target=self._cache_cleanup_loop,
            name="DataHandler-CacheCleanup",
            daemon=True
        )
        self.cache_cleanup_thread.start()
        logger.info("Cache cleanup task started")

    def _cache_cleanup_loop(self) -> None:
        """Cache cleanup background loop"""
        while self.cleanup_active:
            try:
                self._cleanup_expired_cache()
                # Run cleanup every hour
                time.sleep(3600)

            except Exception as e:
                logger.error(f"Error in cache cleanup loop: {e}")
                time.sleep(3600)

    def _cleanup_expired_cache(self) -> None:
        """Clean up expired cache files"""
        try:
            if not self.cache_dir.exists():
                return

            current_time = datetime.now()
            expired_files = []

            for cache_file in self.cache_dir.glob("*.pkl"):
                try:
                    file_age = current_time - datetime.fromtimestamp(cache_file.stat().st_mtime)
                    if file_age > timedelta(hours=self.cache_expiry_hours):
                        expired_files.append(cache_file)

                except Exception as e:
                    logger.error(f"Error checking cache file {cache_file}: {e}")

            # Delete expired files
            for cache_file in expired_files:
                try:
                    cache_file.unlink()
                    logger.debug(f"Deleted expired cache file: {cache_file.name}")
                except Exception as e:
                    logger.error(f"Error deleting cache file {cache_file}: {e}")

            if expired_files:
                logger.info(f"Cleaned up {len(expired_files)} expired cache files")

        except Exception as e:
            logger.error(f"Error in cache cleanup: {e}")

    def clear_cache(self) -> bool:
        """
        Clear all cached data

        Returns:
            True if cache cleared successfully
        """
        try:
            if not self.cache_dir.exists():
                return True

            # Delete all cache files
            for cache_file in self.cache_dir.glob("*.pkl"):
                cache_file.unlink()

            logger.info("All cache files cleared")
            return True

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics

        Returns:
            Dictionary with cache statistics
        """
        try:
            if not self.cache_dir.exists():
                return {'enabled': False}

            cache_files = list(self.cache_dir.glob("*.pkl"))
            total_size = sum(f.stat().st_size for f in cache_files)

            return {
                'enabled': self.cache_enabled,
                'cache_dir': str(self.cache_dir),
                'file_count': len(cache_files),
                'total_size_mb': total_size / (1024 * 1024),
                'expiry_hours': self.cache_expiry_hours
            }

        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'enabled': self.cache_enabled, 'error': str(e)}

    def stop(self) -> None:
        """Stop data handler and cleanup resources"""
        # Stop cache cleanup
        self.cleanup_active = False
        if self.cache_cleanup_thread:
            self.cache_cleanup_thread.join(timeout=5)

        # Stop market data streaming
        self.market_data.stop_streaming()

        logger.info("Data handler stopped")

    def __del__(self):
        """Destructor"""
        self.stop()
