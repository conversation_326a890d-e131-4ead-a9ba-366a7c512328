#!/usr/bin/env python3
"""
Market Data Module for MT5 Trading Bot
Handles real-time and historical market data from MT5
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import logging
from typing import Optional, Dict, List, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import threading
import time
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class Timeframe(Enum):
    """MT5 Timeframes"""
    M1 = mt5.TIMEFRAME_M1
    M5 = mt5.TIMEFRAME_M5
    M15 = mt5.TIMEFRAME_M15
    M30 = mt5.TIMEFRAME_M30
    H1 = mt5.TIMEFRAME_H1
    H4 = mt5.TIMEFRAME_H4
    D1 = mt5.TIMEFRAME_D1
    W1 = mt5.TIMEFRAME_W1
    MN1 = mt5.TIMEFRAME_MN1


@dataclass
class Tick:
    """Tick data structure"""
    symbol: str
    time: datetime
    bid: float
    ask: float
    last: float
    volume: int
    flags: int

    @property
    def spread(self) -> float:
        """Calculate spread in points"""
        return self.ask - self.bid

    @property
    def mid_price(self) -> float:
        """Calculate mid price"""
        return (self.bid + self.ask) / 2


@dataclass
class OHLCV:
    """OHLCV bar data structure"""
    symbol: str
    timeframe: Timeframe
    time: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    spread: int = 0

    @property
    def body_size(self) -> float:
        """Calculate candle body size"""
        return abs(self.close - self.open)

    @property
    def upper_shadow(self) -> float:
        """Calculate upper shadow size"""
        return self.high - max(self.open, self.close)

    @property
    def lower_shadow(self) -> float:
        """Calculate lower shadow size"""
        return min(self.open, self.close) - self.low

    @property
    def is_bullish(self) -> bool:
        """Check if candle is bullish"""
        return self.close > self.open

    @property
    def is_bearish(self) -> bool:
        """Check if candle is bearish"""
        return self.close < self.open

    @property
    def is_doji(self, threshold: float = 0.1) -> bool:
        """Check if candle is doji (small body relative to range)"""
        total_range = self.high - self.low
        if total_range == 0:
            return True
        return (self.body_size / total_range) < threshold


class MarketData:
    """Handles market data operations with MT5"""

    def __init__(self, connection_manager):
        """
        Initialize MarketData

        Args:
            connection_manager: MT5 connection manager instance
        """
        self.connection_manager = connection_manager
        self.symbol_info_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_lock = threading.Lock()

        # Real-time data streams
        self.tick_subscribers: Dict[str, List[callable]] = defaultdict(list)
        self.bar_subscribers: Dict[Tuple[str, Timeframe], List[callable]] = defaultdict(list)

        # Data buffers
        self.tick_buffers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.bar_buffers: Dict[Tuple[str, Timeframe], deque] = defaultdict(lambda: deque(maxlen=1000))

        # Streaming control
        self.streaming_active = False
        self.streaming_thread: Optional[threading.Thread] = None

    def get_symbol_info(self, symbol: str, force_refresh: bool = False) -> Optional[Dict[str, Any]]:
        """
        Get symbol information with caching

        Args:
            symbol: Symbol name
            force_refresh: Force refresh from MT5

        Returns:
            Symbol information dictionary or None if failed
        """
        with self.cache_lock:
            if not force_refresh and symbol in self.symbol_info_cache:
                return self.symbol_info_cache[symbol]

            connection = self.connection_manager.get_connection()
            if not connection:
                logger.error("No MT5 connection available")
                return None

            symbol_info = connection.get_symbol_info(symbol)
            if symbol_info:
                self.symbol_info_cache[symbol] = symbol_info

            return symbol_info

    def get_current_tick(self, symbol: str) -> Optional[Tick]:
        """
        Get current tick for symbol

        Args:
            symbol: Symbol name

        Returns:
            Current tick data or None if failed
        """
        connection = self.connection_manager.get_connection()
        if not connection:
            logger.error("No MT5 connection available")
            return None

        tick_data = connection.get_tick(symbol)
        if not tick_data:
            return None

        return Tick(
            symbol=symbol,
            time=tick_data['time'],
            bid=tick_data['bid'],
            ask=tick_data['ask'],
            last=tick_data['last'],
            volume=tick_data['volume'],
            flags=tick_data['flags']
        )

    def get_historical_data(self, symbol: str, timeframe: Timeframe,
                           count: int = 1000,
                           start_time: Optional[datetime] = None) -> Optional[pd.DataFrame]:
        """
        Get historical OHLCV data

        Args:
            symbol: Symbol name
            timeframe: Data timeframe
            count: Number of bars to retrieve
            start_time: Start time for data (optional)

        Returns:
            DataFrame with OHLCV data or None if failed
        """
        connection = self.connection_manager.get_connection()
        if not connection:
            logger.error("No MT5 connection available")
            return None

        try:
            if start_time:
                rates = mt5.copy_rates_from(symbol, timeframe.value, start_time, count)
            else:
                rates = mt5.copy_rates_from_pos(symbol, timeframe.value, 0, count)

            if rates is None or len(rates) == 0:
                logger.warning(f"No historical data available for {symbol} {timeframe.name}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)

            # Rename columns to standard format
            df.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'tick_volume': 'Volume'
            }, inplace=True)

            logger.debug(f"Retrieved {len(df)} bars for {symbol} {timeframe.name}")
            return df

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return None

    def get_historical_bars(self, symbol: str, timeframe: Timeframe,
                           count: int = 1000,
                           start_time: Optional[datetime] = None) -> List[OHLCV]:
        """
        Get historical data as OHLCV objects

        Args:
            symbol: Symbol name
            timeframe: Data timeframe
            count: Number of bars to retrieve
            start_time: Start time for data (optional)

        Returns:
            List of OHLCV objects
        """
        df = self.get_historical_data(symbol, timeframe, count, start_time)
        if df is None:
            return []

        bars = []
        for index, row in df.iterrows():
            bar = OHLCV(
                symbol=symbol,
                timeframe=timeframe,
                time=index,
                open=row['Open'],
                high=row['High'],
                low=row['Low'],
                close=row['Close'],
                volume=int(row['Volume']),
                spread=int(row.get('spread', 0))
            )
            bars.append(bar)

        return bars

    def get_tick_data(self, symbol: str, start_time: datetime,
                     end_time: Optional[datetime] = None,
                     count: int = 1000) -> Optional[pd.DataFrame]:
        """
        Get historical tick data

        Args:
            symbol: Symbol name
            start_time: Start time for ticks
            end_time: End time for ticks (optional)
            count: Maximum number of ticks

        Returns:
            DataFrame with tick data or None if failed
        """
        connection = self.connection_manager.get_connection()
        if not connection:
            logger.error("No MT5 connection available")
            return None

        try:
            if end_time:
                ticks = mt5.copy_ticks_range(symbol, start_time, end_time, mt5.COPY_TICKS_ALL)
            else:
                ticks = mt5.copy_ticks_from(symbol, start_time, count, mt5.COPY_TICKS_ALL)

            if ticks is None or len(ticks) == 0:
                logger.warning(f"No tick data available for {symbol}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(ticks)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)

            logger.debug(f"Retrieved {len(df)} ticks for {symbol}")
            return df

        except Exception as e:
            logger.error(f"Error getting tick data for {symbol}: {e}")
            return None

    def subscribe_to_ticks(self, symbol: str, callback: callable) -> bool:
        """
        Subscribe to real-time tick updates

        Args:
            symbol: Symbol to subscribe to
            callback: Callback function to receive tick updates

        Returns:
            True if subscription successful
        """
        try:
            self.tick_subscribers[symbol].append(callback)

            # Start streaming if not already active
            if not self.streaming_active:
                self.start_streaming()

            logger.info(f"Subscribed to tick updates for {symbol}")
            return True

        except Exception as e:
            logger.error(f"Error subscribing to ticks for {symbol}: {e}")
            return False

    def unsubscribe_from_ticks(self, symbol: str, callback: callable) -> bool:
        """
        Unsubscribe from tick updates

        Args:
            symbol: Symbol to unsubscribe from
            callback: Callback function to remove

        Returns:
            True if unsubscription successful
        """
        try:
            if symbol in self.tick_subscribers:
                self.tick_subscribers[symbol].remove(callback)

                # Clean up empty subscriber lists
                if not self.tick_subscribers[symbol]:
                    del self.tick_subscribers[symbol]

            logger.info(f"Unsubscribed from tick updates for {symbol}")
            return True

        except Exception as e:
            logger.error(f"Error unsubscribing from ticks for {symbol}: {e}")
            return False

    def start_streaming(self) -> bool:
        """
        Start real-time data streaming

        Returns:
            True if streaming started successfully
        """
        if self.streaming_active:
            return True

        try:
            self.streaming_active = True
            self.streaming_thread = threading.Thread(
                target=self._streaming_loop,
                name="MarketData-Streaming",
                daemon=True
            )
            self.streaming_thread.start()

            logger.info("Market data streaming started")
            return True

        except Exception as e:
            logger.error(f"Error starting streaming: {e}")
            self.streaming_active = False
            return False

    def stop_streaming(self) -> None:
        """Stop real-time data streaming"""
        self.streaming_active = False

        if self.streaming_thread:
            self.streaming_thread.join(timeout=5)

        logger.info("Market data streaming stopped")

    def get_cached_bars(self, symbol: str, timeframe: Timeframe, count: int = 100) -> List[OHLCV]:
        """
        Get cached bar data

        Args:
            symbol: Symbol name
            timeframe: Timeframe
            count: Number of bars to return

        Returns:
            List of cached OHLCV bars
        """
        key = (symbol, timeframe)
        if key in self.bar_buffers:
            buffer = self.bar_buffers[key]
            return list(buffer)[-count:] if len(buffer) >= count else list(buffer)
        return []

    def get_cached_ticks(self, symbol: str, count: int = 100) -> List[Tick]:
        """
        Get cached tick data

        Args:
            symbol: Symbol name
            count: Number of ticks to return

        Returns:
            List of cached ticks
        """
        if symbol in self.tick_buffers:
            buffer = self.tick_buffers[symbol]
            return list(buffer)[-count:] if len(buffer) >= count else list(buffer)
        return []

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate common technical indicators

        Args:
            df: OHLCV DataFrame

        Returns:
            DataFrame with added indicators
        """
        if df is None or df.empty:
            return df

        try:
            # Simple Moving Averages
            df['SMA_10'] = df['Close'].rolling(window=10).mean()
            df['SMA_20'] = df['Close'].rolling(window=20).mean()
            df['SMA_50'] = df['Close'].rolling(window=50).mean()

            # Exponential Moving Averages
            df['EMA_10'] = df['Close'].ewm(span=10).mean()
            df['EMA_20'] = df['Close'].ewm(span=20).mean()

            # RSI
            delta = df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))

            # Bollinger Bands
            df['BB_Middle'] = df['Close'].rolling(window=20).mean()
            bb_std = df['Close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)

            # MACD
            ema_12 = df['Close'].ewm(span=12).mean()
            ema_26 = df['Close'].ewm(span=26).mean()
            df['MACD'] = ema_12 - ema_26
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']

            # Stochastic
            low_14 = df['Low'].rolling(window=14).min()
            high_14 = df['High'].rolling(window=14).max()
            df['Stoch_K'] = 100 * ((df['Close'] - low_14) / (high_14 - low_14))
            df['Stoch_D'] = df['Stoch_K'].rolling(window=3).mean()

            # Average True Range (ATR)
            high_low = df['High'] - df['Low']
            high_close = np.abs(df['High'] - df['Close'].shift())
            low_close = np.abs(df['Low'] - df['Close'].shift())
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            df['ATR'] = true_range.rolling(window=14).mean()

            return df

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return df

    def _streaming_loop(self) -> None:
        """Main streaming loop for real-time data"""
        while self.streaming_active:
            try:
                # Update tick data for subscribed symbols
                for symbol in self.tick_subscribers.keys():
                    tick = self.get_current_tick(symbol)
                    if tick:
                        # Add to buffer
                        self.tick_buffers[symbol].append(tick)

                        # Notify subscribers
                        for callback in self.tick_subscribers[symbol]:
                            try:
                                callback(tick)
                            except Exception as e:
                                logger.error(f"Error in tick callback for {symbol}: {e}")

                # Sleep to avoid overwhelming the connection
                time.sleep(0.1)  # 100ms update interval

            except Exception as e:
                logger.error(f"Error in streaming loop: {e}")
                time.sleep(1)

    def validate_symbol(self, symbol: str) -> bool:
        """
        Validate if symbol exists and is tradeable

        Args:
            symbol: Symbol to validate

        Returns:
            True if symbol is valid and tradeable
        """
        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            return False

        # Check if symbol is visible and tradeable
        return (symbol_info.get('visible', False) and
                symbol_info.get('trade_mode', 0) != 0)

    def get_market_hours(self, symbol: str) -> Dict[str, Any]:
        """
        Get market hours information for symbol

        Args:
            symbol: Symbol name

        Returns:
            Dictionary with market hours information
        """
        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            return {}

        return {
            'session_deals': symbol_info.get('session_deals', 0),
            'session_buy_orders': symbol_info.get('session_buy_orders', 0),
            'session_sell_orders': symbol_info.get('session_sell_orders', 0),
            'session_open': symbol_info.get('session_open', 0),
            'session_close': symbol_info.get('session_close', 0)
        }

    def is_market_open(self, symbol: str) -> bool:
        """
        Check if market is currently open for symbol

        Args:
            symbol: Symbol name

        Returns:
            True if market is open
        """
        # Simple check - in a real implementation, you'd want to check
        # actual market hours based on symbol and current time
        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            return False

        # Check if there are recent deals (indicates active market)
        return symbol_info.get('session_deals', 0) > 0

    def get_spread(self, symbol: str) -> Optional[float]:
        """
        Get current spread for symbol

        Args:
            symbol: Symbol name

        Returns:
            Current spread in points or None if failed
        """
        tick = self.get_current_tick(symbol)
        if tick:
            return tick.spread
        return None

    def __del__(self):
        """Destructor"""
        self.stop_streaming()
