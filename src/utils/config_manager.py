#!/usr/bin/env python3
"""
Configuration Manager for MT5 Trading Bot
Handles loading and validation of configuration from YAML files and environment variables
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dotenv import load_dotenv
import re

logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages configuration loading and validation"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize ConfigManager
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path or self._get_default_config_path()
        self.config: Dict[str, Any] = {}
        self._load_environment()
        self._load_config()
        
    def _get_default_config_path(self) -> str:
        """Get default configuration file path"""
        current_dir = Path(__file__).parent.parent.parent
        return str(current_dir / "config" / "config.yaml")
        
    def _load_environment(self) -> None:
        """Load environment variables from .env file"""
        env_path = Path(__file__).parent.parent.parent / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            logger.info(f"Loaded environment variables from {env_path}")
        else:
            logger.warning(f"No .env file found at {env_path}")
            
    def _load_config(self) -> None:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                raw_config = yaml.safe_load(file)
                
            # Substitute environment variables
            self.config = self._substitute_env_vars(raw_config)
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            raise
            
    def _substitute_env_vars(self, obj: Any) -> Any:
        """
        Recursively substitute environment variables in configuration
        
        Args:
            obj: Configuration object to process
            
        Returns:
            Processed configuration with environment variables substituted
        """
        if isinstance(obj, dict):
            return {key: self._substitute_env_vars(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_env_vars(item) for item in obj]
        elif isinstance(obj, str):
            return self._substitute_env_var_string(obj)
        else:
            return obj
            
    def _substitute_env_var_string(self, value: str) -> Union[str, int, float, bool]:
        """
        Substitute environment variables in a string
        
        Args:
            value: String that may contain environment variable references
            
        Returns:
            String with environment variables substituted and type converted
        """
        # Pattern to match ${VAR_NAME} or ${VAR_NAME:default_value}
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        
        def replace_env_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ""
            return os.getenv(var_name, default_value)
            
        result = re.sub(pattern, replace_env_var, value)
        
        # Try to convert to appropriate type
        return self._convert_type(result)
        
    def _convert_type(self, value: str) -> Union[str, int, float, bool]:
        """
        Convert string value to appropriate type
        
        Args:
            value: String value to convert
            
        Returns:
            Converted value
        """
        if value.lower() in ('true', 'yes', '1'):
            return True
        elif value.lower() in ('false', 'no', '0'):
            return False
        
        # Try to convert to number
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            return value
            
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key
        
        Args:
            key: Configuration key (supports dot notation, e.g., 'mt5.login')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value
        
        Args:
            key: Configuration key (supports dot notation)
            value: Value to set
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        config[keys[-1]] = value
        
    def validate(self) -> bool:
        """
        Validate configuration
        
        Returns:
            True if configuration is valid
        """
        required_keys = [
            'mt5.login',
            'mt5.password',
            'mt5.server',
            'trading.max_risk_per_trade',
            'risk.stop_loss_pips',
            'database.path'
        ]
        
        missing_keys = []
        for key in required_keys:
            if self.get(key) is None:
                missing_keys.append(key)
                
        if missing_keys:
            logger.error(f"Missing required configuration keys: {missing_keys}")
            return False
            
        # Validate value ranges
        if not (0 < self.get('trading.max_risk_per_trade', 0) <= 1):
            logger.error("trading.max_risk_per_trade must be between 0 and 1")
            return False
            
        if not (0 < self.get('risk.stop_loss_pips', 0) <= 1000):
            logger.error("risk.stop_loss_pips must be between 0 and 1000")
            return False
            
        logger.info("Configuration validation passed")
        return True
        
    def get_mt5_config(self) -> Dict[str, Any]:
        """Get MT5 connection configuration"""
        return self.get('mt5', {})
        
    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading configuration"""
        return self.get('trading', {})
        
    def get_risk_config(self) -> Dict[str, Any]:
        """Get risk management configuration"""
        return self.get('risk', {})
        
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return self.get('database', {})
        
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return self.get('logging', {})
        
    def is_trading_enabled(self) -> bool:
        """Check if trading is enabled"""
        return self.get('trading.enabled', False)
        
    def is_paper_trading(self) -> bool:
        """Check if paper trading mode is enabled"""
        return self.get('development.paper_trading', True)
        
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled"""
        return self.get('development.debug_mode', False)
        
    def reload(self) -> None:
        """Reload configuration from file"""
        self._load_environment()
        self._load_config()
        logger.info("Configuration reloaded")
        
    def __str__(self) -> str:
        """String representation of configuration (sensitive data masked)"""
        safe_config = self._mask_sensitive_data(self.config.copy())
        return yaml.dump(safe_config, default_flow_style=False)
        
    def _mask_sensitive_data(self, obj: Any) -> Any:
        """Mask sensitive data in configuration for logging"""
        sensitive_keys = ['password', 'login', 'username', 'token', 'key', 'secret']
        
        if isinstance(obj, dict):
            return {
                key: '***MASKED***' if any(sensitive in key.lower() for sensitive in sensitive_keys)
                else self._mask_sensitive_data(value)
                for key, value in obj.items()
            }
        elif isinstance(obj, list):
            return [self._mask_sensitive_data(item) for item in obj]
        else:
            return obj


# Global configuration instance
config_manager = ConfigManager()
