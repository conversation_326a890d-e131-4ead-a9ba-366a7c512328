#!/usr/bin/env python3
"""
Comprehensive Logging System for MT5 Trading Bot
Provides structured logging with multiple handlers and formatters
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import json
import traceback
from .config_manager import config_manager


class TradingBotFormatter(logging.Formatter):
    """Custom formatter for trading bot logs"""
    
    def __init__(self, include_extra: bool = True):
        """
        Initialize formatter
        
        Args:
            include_extra: Whether to include extra fields in log records
        """
        self.include_extra = include_extra
        super().__init__()
        
    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record
        
        Args:
            record: Log record to format
            
        Returns:
            Formatted log string
        """
        # Base format
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        level = record.levelname
        name = record.name
        message = record.getMessage()
        
        # Build base log entry
        log_entry = f"{timestamp} | {level:8} | {name:20} | {message}"
        
        # Add extra information if available
        if self.include_extra and hasattr(record, 'extra_data'):
            extra_str = json.dumps(record.extra_data, default=str)
            log_entry += f" | EXTRA: {extra_str}"
            
        # Add exception information if present
        if record.exc_info:
            log_entry += f"\n{self.formatException(record.exc_info)}"
            
        return log_entry


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as JSON
        
        Args:
            record: Log record to format
            
        Returns:
            JSON formatted log string
        """
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process
        }
        
        # Add extra data if available
        if hasattr(record, 'extra_data'):
            log_data['extra'] = record.extra_data
            
        # Add exception information if present
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
            
        return json.dumps(log_data, default=str)


class TradingLogger:
    """Enhanced logger for trading bot with multiple handlers and formatters"""
    
    def __init__(self, name: str = "TradingBot", config: Optional[Dict[str, Any]] = None):
        """
        Initialize trading logger
        
        Args:
            name: Logger name
            config: Logging configuration
        """
        self.name = name
        self.config = config or config_manager.get_logging_config()
        self.logger = logging.getLogger(name)
        self.handlers_configured = False
        
        self._setup_logger()
        
    def _setup_logger(self) -> None:
        """Setup logger with handlers and formatters"""
        if self.handlers_configured:
            return
            
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        level = getattr(logging, self.config.get('level', 'INFO').upper())
        self.logger.setLevel(level)
        
        # Create formatters
        text_formatter = TradingBotFormatter(include_extra=True)
        json_formatter = JSONFormatter()
        
        # Console handler
        if self.config.get('console_output', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(text_formatter)
            console_handler.setLevel(level)
            self.logger.addHandler(console_handler)
            
        # File handler
        file_path = self.config.get('file_path', 'logs/trading_bot.log')
        if file_path:
            self._setup_file_handler(file_path, text_formatter)
            
        # JSON file handler for structured logs
        json_file_path = file_path.replace('.log', '_structured.json') if file_path else None
        if json_file_path:
            self._setup_json_file_handler(json_file_path, json_formatter)
            
        # Error file handler
        error_file_path = file_path.replace('.log', '_errors.log') if file_path else None
        if error_file_path:
            self._setup_error_file_handler(error_file_path, text_formatter)
            
        self.handlers_configured = True
        
    def _setup_file_handler(self, file_path: str, formatter: logging.Formatter) -> None:
        """Setup rotating file handler"""
        try:
            # Ensure directory exists
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Create rotating file handler
            max_size = self.config.get('max_size_mb', 100) * 1024 * 1024  # Convert MB to bytes
            backup_count = self.config.get('backup_count', 5)
            
            file_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=max_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)
            self.logger.addHandler(file_handler)
            
        except Exception as e:
            print(f"Failed to setup file handler: {e}")
            
    def _setup_json_file_handler(self, file_path: str, formatter: logging.Formatter) -> None:
        """Setup JSON file handler for structured logging"""
        try:
            # Ensure directory exists
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Create rotating file handler for JSON logs
            max_size = self.config.get('max_size_mb', 100) * 1024 * 1024
            backup_count = self.config.get('backup_count', 5)
            
            json_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=max_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            json_handler.setFormatter(formatter)
            json_handler.setLevel(logging.INFO)
            self.logger.addHandler(json_handler)
            
        except Exception as e:
            print(f"Failed to setup JSON file handler: {e}")
            
    def _setup_error_file_handler(self, file_path: str, formatter: logging.Formatter) -> None:
        """Setup error-only file handler"""
        try:
            # Ensure directory exists
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Create file handler for errors only
            error_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=50 * 1024 * 1024,  # 50MB for errors
                backupCount=10,
                encoding='utf-8'
            )
            error_handler.setFormatter(formatter)
            error_handler.setLevel(logging.ERROR)
            self.logger.addHandler(error_handler)
            
        except Exception as e:
            print(f"Failed to setup error file handler: {e}")
            
    def log_trade(self, action: str, symbol: str, volume: float, price: float, 
                  order_type: str, **kwargs) -> None:
        """
        Log trading action with structured data
        
        Args:
            action: Trading action (BUY, SELL, CLOSE, etc.)
            symbol: Trading symbol
            volume: Trade volume
            price: Trade price
            order_type: Order type
            **kwargs: Additional trade data
        """
        extra_data = {
            'event_type': 'trade',
            'action': action,
            'symbol': symbol,
            'volume': volume,
            'price': price,
            'order_type': order_type,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        message = f"TRADE {action}: {symbol} {volume} lots @ {price} ({order_type})"
        self.logger.info(message, extra={'extra_data': extra_data})
        
    def log_signal(self, strategy: str, symbol: str, signal: str, confidence: float = None, **kwargs) -> None:
        """
        Log trading signal
        
        Args:
            strategy: Strategy name
            symbol: Trading symbol
            signal: Signal type (BUY, SELL, HOLD)
            confidence: Signal confidence (0-1)
            **kwargs: Additional signal data
        """
        extra_data = {
            'event_type': 'signal',
            'strategy': strategy,
            'symbol': symbol,
            'signal': signal,
            'confidence': confidence,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        message = f"SIGNAL {strategy}: {symbol} -> {signal}"
        if confidence is not None:
            message += f" (confidence: {confidence:.2f})"
            
        self.logger.info(message, extra={'extra_data': extra_data})
        
    def log_performance(self, metric: str, value: float, period: str = None, **kwargs) -> None:
        """
        Log performance metric
        
        Args:
            metric: Metric name (profit, drawdown, win_rate, etc.)
            value: Metric value
            period: Time period (daily, weekly, monthly)
            **kwargs: Additional performance data
        """
        extra_data = {
            'event_type': 'performance',
            'metric': metric,
            'value': value,
            'period': period,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        message = f"PERFORMANCE {metric}: {value}"
        if period:
            message += f" ({period})"
            
        self.logger.info(message, extra={'extra_data': extra_data})
        
    def log_error(self, error: Exception, context: str = None, **kwargs) -> None:
        """
        Log error with context
        
        Args:
            error: Exception object
            context: Error context description
            **kwargs: Additional error data
        """
        extra_data = {
            'event_type': 'error',
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        message = f"ERROR: {error}"
        if context:
            message = f"ERROR in {context}: {error}"
            
        self.logger.error(message, extra={'extra_data': extra_data}, exc_info=True)
        
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message"""
        self.logger.debug(message, extra={'extra_data': kwargs} if kwargs else None)
        
    def info(self, message: str, **kwargs) -> None:
        """Log info message"""
        self.logger.info(message, extra={'extra_data': kwargs} if kwargs else None)
        
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message"""
        self.logger.warning(message, extra={'extra_data': kwargs} if kwargs else None)
        
    def error(self, message: str, **kwargs) -> None:
        """Log error message"""
        self.logger.error(message, extra={'extra_data': kwargs} if kwargs else None)
        
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message"""
        self.logger.critical(message, extra={'extra_data': kwargs} if kwargs else None)


# Global logger instance
trading_logger = TradingLogger()
