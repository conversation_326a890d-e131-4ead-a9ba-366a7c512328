#!/usr/bin/env python3
"""
MT5 Connection Module
Handles MetaTrader 5 connection, authentication, and basic operations
"""

import MetaTrader5 as mt5
import logging
import time
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
import pandas as pd

logger = logging.getLogger(__name__)


class MT5Connection:
    """Manages MetaTrader 5 connection and basic operations"""
    
    def __init__(self, login: int, password: str, server: str, path: Optional[str] = None):
        """
        Initialize MT5 connection
        
        Args:
            login: MT5 account login
            password: MT5 account password
            server: MT5 server name
            path: Path to MT5 terminal (optional)
        """
        self.login = login
        self.password = password
        self.server = server
        self.path = path
        self.connected = False
        self.account_info = None
        self.last_heartbeat = None
        
    def connect(self) -> bool:
        """
        Establish connection to MT5
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Initialize MT5
            if self.path:
                if not mt5.initialize(path=self.path):
                    logger.error(f"MT5 initialize failed with path: {self.path}")
                    return False
            else:
                if not mt5.initialize():
                    logger.error("MT5 initialize failed")
                    return False
                    
            # Login to account
            if not mt5.login(self.login, password=self.password, server=self.server):
                error = mt5.last_error()
                logger.error(f"MT5 login failed: {error}")
                mt5.shutdown()
                return False
                
            # Get account info
            self.account_info = mt5.account_info()
            if self.account_info is None:
                logger.error("Failed to get account info")
                mt5.shutdown()
                return False
                
            self.connected = True
            self.last_heartbeat = datetime.now()
            
            logger.info(f"Successfully connected to MT5 - Account: {self.login}, Server: {self.server}")
            logger.info(f"Account Info - Balance: {self.account_info.balance}, "
                       f"Equity: {self.account_info.equity}, "
                       f"Currency: {self.account_info.currency}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to MT5: {e}")
            return False
            
    def disconnect(self) -> None:
        """Disconnect from MT5"""
        try:
            if self.connected:
                mt5.shutdown()
                self.connected = False
                logger.info("Disconnected from MT5")
        except Exception as e:
            logger.error(f"Error disconnecting from MT5: {e}")
            
    def is_connected(self) -> bool:
        """
        Check if connection is active
        
        Returns:
            True if connected, False otherwise
        """
        if not self.connected:
            return False
            
        try:
            # Try to get account info as a heartbeat
            account_info = mt5.account_info()
            if account_info is None:
                self.connected = False
                return False
                
            self.last_heartbeat = datetime.now()
            return True
            
        except Exception as e:
            logger.error(f"Connection check failed: {e}")
            self.connected = False
            return False
            
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """
        Get account information
        
        Returns:
            Account information dictionary or None if failed
        """
        try:
            if not self.is_connected():
                return None
                
            account_info = mt5.account_info()
            if account_info is None:
                return None
                
            return {
                'login': account_info.login,
                'server': account_info.server,
                'name': account_info.name,
                'company': account_info.company,
                'currency': account_info.currency,
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'margin_free': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'profit': account_info.profit,
                'credit': account_info.credit,
                'trade_allowed': account_info.trade_allowed,
                'trade_expert': account_info.trade_expert,
                'leverage': account_info.leverage,
                'margin_so_mode': account_info.margin_so_mode,
                'margin_so_call': account_info.margin_so_call,
                'margin_so_so': account_info.margin_so_so,
                'currency_digits': account_info.currency_digits,
                'fifo_close': account_info.fifo_close
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None
            
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get symbol information
        
        Args:
            symbol: Symbol name (e.g., 'EURUSD')
            
        Returns:
            Symbol information dictionary or None if failed
        """
        try:
            if not self.is_connected():
                return None
                
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.warning(f"Symbol {symbol} not found")
                return None
                
            return {
                'name': symbol_info.name,
                'basis': symbol_info.basis,
                'category': symbol_info.category,
                'currency_base': symbol_info.currency_base,
                'currency_profit': symbol_info.currency_profit,
                'currency_margin': symbol_info.currency_margin,
                'digits': symbol_info.digits,
                'trade_tick_value': symbol_info.trade_tick_value,
                'trade_tick_value_profit': symbol_info.trade_tick_value_profit,
                'trade_tick_value_loss': symbol_info.trade_tick_value_loss,
                'trade_tick_size': symbol_info.trade_tick_size,
                'trade_contract_size': symbol_info.trade_contract_size,
                'volume_min': symbol_info.volume_min,
                'volume_max': symbol_info.volume_max,
                'volume_step': symbol_info.volume_step,
                'spread': symbol_info.spread,
                'point': symbol_info.point,
                'trade_mode': symbol_info.trade_mode,
                'trade_allowed': symbol_info.trade_allowed,
                'trade_stops_level': symbol_info.trade_stops_level,
                'trade_freeze_level': symbol_info.trade_freeze_level,
                'margin_initial': symbol_info.margin_initial,
                'margin_maintenance': symbol_info.margin_maintenance,
                'session_deals': symbol_info.session_deals,
                'session_buy_orders': symbol_info.session_buy_orders,
                'session_sell_orders': symbol_info.session_sell_orders,
                'session_turnover': symbol_info.session_turnover,
                'session_interest': symbol_info.session_interest,
                'session_buy_orders_volume': symbol_info.session_buy_orders_volume,
                'session_sell_orders_volume': symbol_info.session_sell_orders_volume,
                'session_open': symbol_info.session_open,
                'session_close': symbol_info.session_close,
                'session_aw': symbol_info.session_aw,
                'session_price_settlement': symbol_info.session_price_settlement,
                'session_price_limit_min': symbol_info.session_price_limit_min,
                'session_price_limit_max': symbol_info.session_price_limit_max
            }
            
        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {e}")
            return None
            
    def get_tick(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get current tick for symbol
        
        Args:
            symbol: Symbol name
            
        Returns:
            Tick information dictionary or None if failed
        """
        try:
            if not self.is_connected():
                return None
                
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return None
                
            return {
                'time': datetime.fromtimestamp(tick.time),
                'bid': tick.bid,
                'ask': tick.ask,
                'last': tick.last,
                'volume': tick.volume,
                'time_msc': tick.time_msc,
                'flags': tick.flags,
                'volume_real': tick.volume_real
            }
            
        except Exception as e:
            logger.error(f"Error getting tick for {symbol}: {e}")
            return None
            
    def get_positions(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get open positions
        
        Args:
            symbol: Filter by symbol (optional)
            
        Returns:
            List of position dictionaries
        """
        try:
            if not self.is_connected():
                return []
                
            if symbol:
                positions = mt5.positions_get(symbol=symbol)
            else:
                positions = mt5.positions_get()
                
            if positions is None:
                return []
                
            result = []
            for pos in positions:
                result.append({
                    'ticket': pos.ticket,
                    'time': datetime.fromtimestamp(pos.time),
                    'time_msc': pos.time_msc,
                    'time_update': datetime.fromtimestamp(pos.time_update),
                    'time_update_msc': pos.time_update_msc,
                    'type': pos.type,
                    'magic': pos.magic,
                    'identifier': pos.identifier,
                    'reason': pos.reason,
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'sl': pos.sl,
                    'tp': pos.tp,
                    'price_current': pos.price_current,
                    'swap': pos.swap,
                    'profit': pos.profit,
                    'symbol': pos.symbol,
                    'comment': pos.comment,
                    'external_id': pos.external_id
                })
                
            return result
            
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
            
    def get_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get pending orders
        
        Args:
            symbol: Filter by symbol (optional)
            
        Returns:
            List of order dictionaries
        """
        try:
            if not self.is_connected():
                return []
                
            if symbol:
                orders = mt5.orders_get(symbol=symbol)
            else:
                orders = mt5.orders_get()
                
            if orders is None:
                return []
                
            result = []
            for order in orders:
                result.append({
                    'ticket': order.ticket,
                    'time_setup': datetime.fromtimestamp(order.time_setup),
                    'time_setup_msc': order.time_setup_msc,
                    'time_expiration': datetime.fromtimestamp(order.time_expiration) if order.time_expiration > 0 else None,
                    'type': order.type,
                    'type_time': order.type_time,
                    'type_filling': order.type_filling,
                    'state': order.state,
                    'magic': order.magic,
                    'position_id': order.position_id,
                    'position_by_id': order.position_by_id,
                    'reason': order.reason,
                    'volume_initial': order.volume_initial,
                    'volume_current': order.volume_current,
                    'price_open': order.price_open,
                    'sl': order.sl,
                    'tp': order.tp,
                    'price_current': order.price_current,
                    'price_stoplimit': order.price_stoplimit,
                    'symbol': order.symbol,
                    'comment': order.comment,
                    'external_id': order.external_id
                })
                
            return result
            
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
            
    def get_last_error(self) -> Tuple[int, str]:
        """
        Get last MT5 error
        
        Returns:
            Tuple of (error_code, error_description)
        """
        try:
            error_code, error_description = mt5.last_error()
            return error_code, error_description
        except Exception as e:
            logger.error(f"Error getting last error: {e}")
            return -1, str(e)
            
    def __enter__(self):
        """Context manager entry"""
        if not self.connect():
            raise ConnectionError("Failed to connect to MT5")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
        
    def __del__(self):
        """Destructor"""
        self.disconnect()
