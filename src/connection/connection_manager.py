#!/usr/bin/env python3
"""
Connection Manager for MT5 Trading Bot
Handles connection lifecycle, reconnection logic, and error recovery
"""

import logging
import time
import threading
from typing import Optional, Callable, Dict, Any
from datetime import datetime, timedelta
from .mt5_connection import MT5Connection
from ..utils.config_manager import config_manager

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages MT5 connection with automatic reconnection and error handling"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Connection Manager

        Args:
            config: MT5 configuration dictionary
        """
        self.config = config or config_manager.get_mt5_config()
        self.connection: Optional[MT5Connection] = None
        self.is_running = False
        self.reconnect_thread: Optional[threading.Thread] = None
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.connection_callbacks: Dict[str, Callable] = {}

        # Connection settings
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 5)
        self.timeout = self.config.get('timeout', 10)
        self.heartbeat_interval = self.config.get('heartbeat_interval', 30)

        # Connection state
        self.connection_attempts = 0
        self.last_connection_time = None
        self.last_error = None
        self.connection_lock = threading.Lock()

    def connect(self) -> bool:
        """
        Establish connection to MT5

        Returns:
            True if connection successful, False otherwise
        """
        with self.connection_lock:
            try:
                # Validate configuration
                if not self._validate_config():
                    return False

                # Create connection
                self.connection = MT5Connection(
                    login=int(self.config['login']),
                    password=self.config['password'],
                    server=self.config['server'],
                    path=self.config.get('path')
                )

                # Attempt connection
                if self.connection.connect():
                    self.connection_attempts = 0
                    self.last_connection_time = datetime.now()
                    self.last_error = None

                    logger.info("MT5 connection established successfully")
                    self._notify_connection_event('connected')

                    # Start monitoring threads
                    self._start_monitoring()

                    return True
                else:
                    error_code, error_desc = self.connection.get_last_error()
                    self.last_error = f"Connection failed: {error_code} - {error_desc}"
                    logger.error(self.last_error)
                    return False

            except Exception as e:
                self.last_error = f"Connection error: {str(e)}"
                logger.error(self.last_error)
                return False

    def disconnect(self) -> None:
        """Disconnect from MT5 and stop monitoring"""
        with self.connection_lock:
            self.is_running = False

            # Stop monitoring threads
            self._stop_monitoring()

            # Disconnect
            if self.connection:
                self.connection.disconnect()
                self.connection = None

            logger.info("MT5 connection closed")
            self._notify_connection_event('disconnected')

    def is_connected(self) -> bool:
        """
        Check if connection is active

        Returns:
            True if connected, False otherwise
        """
        with self.connection_lock:
            if not self.connection:
                return False
            return self.connection.is_connected()

    def get_connection(self) -> Optional[MT5Connection]:
        """
        Get the current MT5 connection

        Returns:
            MT5Connection instance or None if not connected
        """
        with self.connection_lock:
            if self.is_connected():
                return self.connection
            return None

    def reconnect(self) -> bool:
        """
        Attempt to reconnect to MT5

        Returns:
            True if reconnection successful, False otherwise
        """
        logger.info("Attempting to reconnect to MT5...")

        # Disconnect first
        if self.connection:
            self.connection.disconnect()
            self.connection = None

        # Wait before reconnecting
        time.sleep(self.retry_delay)

        # Attempt reconnection
        return self.connect()

    def start_auto_reconnect(self) -> None:
        """Start automatic reconnection monitoring"""
        if self.reconnect_thread and self.reconnect_thread.is_alive():
            return

        self.is_running = True
        self.reconnect_thread = threading.Thread(
            target=self._auto_reconnect_loop,
            name="MT5-AutoReconnect",
            daemon=True
        )
        self.reconnect_thread.start()
        logger.info("Auto-reconnect monitoring started")

    def stop_auto_reconnect(self) -> None:
        """Stop automatic reconnection monitoring"""
        self.is_running = False
        if self.reconnect_thread:
            self.reconnect_thread.join(timeout=5)
        logger.info("Auto-reconnect monitoring stopped")

    def add_connection_callback(self, event: str, callback: Callable) -> None:
        """
        Add callback for connection events

        Args:
            event: Event type ('connected', 'disconnected', 'reconnecting', 'error')
            callback: Callback function
        """
        if event not in self.connection_callbacks:
            self.connection_callbacks[event] = []
        self.connection_callbacks[event].append(callback)

    def remove_connection_callback(self, event: str, callback: Callable) -> None:
        """
        Remove callback for connection events

        Args:
            event: Event type
            callback: Callback function to remove
        """
        if event in self.connection_callbacks:
            try:
                self.connection_callbacks[event].remove(callback)
            except ValueError:
                pass

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get detailed connection status

        Returns:
            Connection status dictionary
        """
        with self.connection_lock:
            return {
                'connected': self.is_connected(),
                'connection_attempts': self.connection_attempts,
                'last_connection_time': self.last_connection_time,
                'last_error': self.last_error,
                'auto_reconnect_running': self.is_running,
                'config': {
                    'server': self.config.get('server'),
                    'login': self.config.get('login'),
                    'max_retries': self.max_retries,
                    'retry_delay': self.retry_delay,
                    'timeout': self.timeout
                }
            }

    def _validate_config(self) -> bool:
        """
        Validate MT5 configuration

        Returns:
            True if configuration is valid, False otherwise
        """
        required_fields = ['login', 'password', 'server']

        for field in required_fields:
            if not self.config.get(field):
                logger.error(f"Missing required MT5 configuration: {field}")
                return False

        try:
            int(self.config['login'])
        except (ValueError, TypeError):
            logger.error("MT5 login must be a valid integer")
            return False

        return True

    def _start_monitoring(self) -> None:
        """Start connection monitoring threads"""
        if not self.is_running:
            self.is_running = True

            # Start heartbeat monitoring
            if not self.heartbeat_thread or not self.heartbeat_thread.is_alive():
                self.heartbeat_thread = threading.Thread(
                    target=self._heartbeat_loop,
                    name="MT5-Heartbeat",
                    daemon=True
                )
                self.heartbeat_thread.start()

    def _stop_monitoring(self) -> None:
        """Stop connection monitoring threads"""
        self.is_running = False

        # Wait for threads to finish
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
        if self.reconnect_thread:
            self.reconnect_thread.join(timeout=5)

    def _heartbeat_loop(self) -> None:
        """Heartbeat monitoring loop"""
        while self.is_running:
            try:
                if not self.is_connected():
                    logger.warning("Connection lost during heartbeat check")
                    self._notify_connection_event('disconnected')

                    # Trigger reconnection
                    if self.is_running:
                        self._trigger_reconnection()

                time.sleep(self.heartbeat_interval)

            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                time.sleep(self.heartbeat_interval)

    def _auto_reconnect_loop(self) -> None:
        """Automatic reconnection loop"""
        while self.is_running:
            try:
                if not self.is_connected():
                    self._notify_connection_event('reconnecting')

                    if self._attempt_reconnection():
                        logger.info("Reconnection successful")
                    else:
                        logger.warning(f"Reconnection failed, retrying in {self.retry_delay} seconds")

                time.sleep(self.retry_delay)

            except Exception as e:
                logger.error(f"Error in auto-reconnect loop: {e}")
                time.sleep(self.retry_delay)

    def _attempt_reconnection(self) -> bool:
        """
        Attempt reconnection with retry logic

        Returns:
            True if reconnection successful, False otherwise
        """
        for attempt in range(self.max_retries):
            self.connection_attempts += 1

            logger.info(f"Reconnection attempt {attempt + 1}/{self.max_retries}")

            if self.reconnect():
                return True

            if attempt < self.max_retries - 1:
                time.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff

        logger.error(f"Failed to reconnect after {self.max_retries} attempts")
        self._notify_connection_event('error', {'error': 'Max reconnection attempts exceeded'})
        return False

    def _trigger_reconnection(self) -> None:
        """Trigger reconnection process"""
        if not self.reconnect_thread or not self.reconnect_thread.is_alive():
            self.reconnect_thread = threading.Thread(
                target=self._auto_reconnect_loop,
                name="MT5-AutoReconnect",
                daemon=True
            )
            self.reconnect_thread.start()

    def _notify_connection_event(self, event: str, data: Optional[Dict[str, Any]] = None) -> None:
        """
        Notify connection event callbacks

        Args:
            event: Event type
            data: Optional event data
        """
        if event in self.connection_callbacks:
            for callback in self.connection_callbacks[event]:
                try:
                    if data:
                        callback(event, data)
                    else:
                        callback(event)
                except Exception as e:
                    logger.error(f"Error in connection callback: {e}")

    def __enter__(self):
        """Context manager entry"""
        if not self.connect():
            raise ConnectionError("Failed to connect to MT5")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()

    def __del__(self):
        """Destructor"""
        self.disconnect()
