#!/usr/bin/env python3
"""
MT5 Python Trading Bot Setup Script
"""

from setuptools import setup, find_packages
import os

# Read the contents of README file
this_directory = os.path.abspath(os.path.dirname(__file__))
with open(os.path.join(this_directory, 'README.md'), encoding='utf-8') as f:
    long_description = f.read()

# Read requirements
with open('requirements.txt') as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="mt5-python-trading-bot",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A comprehensive MetaTrader 5 Python trading bot with advanced features",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/mt5-python-trading-bot",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.3.0",
            "pytest-asyncio>=0.21.0",
            "pytest-mock>=3.10.0",
            "pytest-cov>=4.0.0",
            "black>=23.3.0",
            "flake8>=6.0.0",
            "mypy>=1.3.0",
            "pre-commit>=3.3.0",
        ],
        "web": [
            "flask>=2.3.0",
            "dash>=2.10.0",
        ],
        "ml": [
            "scikit-learn>=1.2.0",
            "tensorflow>=2.12.0",
            "torch>=2.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "mt5-bot=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt"],
    },
    zip_safe=False,
    keywords="trading, mt5, metatrader, forex, algorithmic-trading, python-bot",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/mt5-python-trading-bot/issues",
        "Source": "https://github.com/yourusername/mt5-python-trading-bot",
        "Documentation": "https://github.com/yourusername/mt5-python-trading-bot/docs",
    },
)
