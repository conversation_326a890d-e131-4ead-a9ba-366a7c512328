# Core MT5 and Trading Libraries
MetaTrader5>=5.0.45
pandas>=2.0.0
numpy>=1.24.0
ta-lib>=0.4.25
TA-Lib>=0.4.25

# Data Analysis and Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# Configuration and Environment
PyYAML>=6.0
python-dotenv>=1.0.0
configparser>=5.3.0

# Database
sqlite3

# Async and Concurrency
asyncio
threading
concurrent.futures

# Logging and Monitoring
loguru>=0.7.0
colorlog>=6.7.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3
schedule>=1.2.0

# Testing
pytest>=7.3.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-cov>=4.0.0

# Development Tools
black>=23.3.0
flake8>=6.0.0
mypy>=1.3.0
pre-commit>=3.3.0

# Optional: Web Interface (if needed)
# flask>=2.3.0
# dash>=2.10.0

# Optional: Advanced Analytics
# scikit-learn>=1.2.0
# tensorflow>=2.12.0
# torch>=2.0.0
