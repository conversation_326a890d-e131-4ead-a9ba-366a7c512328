# Strategy Configuration for MT5 Trading Bot

# SMA Crossover Strategy
sma_crossover:
  name: "Simple Moving Average Crossover"
  description: "Buy when fast SMA crosses above slow SMA, sell when opposite"
  enabled: true
  parameters:
    fast_period: 10
    slow_period: 20
    timeframe: "M15"
    min_crossover_strength: 0.0001  # Minimum price difference for valid signal
  risk:
    stop_loss_pips: 50
    take_profit_pips: 100
    risk_per_trade: 0.02
  symbols:
    - EURUSD
    - GBPUSD
    - USDJPY

# RSI Strategy
rsi_strategy:
  name: "RSI Overbought/Oversold"
  description: "Buy when RSI is oversold, sell when overbought"
  enabled: true
  parameters:
    period: 14
    overbought_level: 70
    oversold_level: 30
    timeframe: "M15"
    confirmation_candles: 2  # Wait for confirmation
  risk:
    stop_loss_pips: 40
    take_profit_pips: 80
    risk_per_trade: 0.015
  symbols:
    - EURUSD
    - GBPUSD
    - AUDUSD

# Bollinger Bands Strategy
bollinger_bands:
  name: "Bollinger Bands Mean Reversion"
  description: "Buy at lower band, sell at upper band"
  enabled: true
  parameters:
    period: 20
    std_dev: 2.0
    timeframe: "M30"
    band_touch_threshold: 0.0005  # How close to band for signal
  risk:
    stop_loss_pips: 60
    take_profit_pips: 120
    risk_per_trade: 0.02
  symbols:
    - EURUSD
    - USDCHF
    - GBPJPY

# MACD Strategy
macd_strategy:
  name: "MACD Signal Line Crossover"
  description: "Buy on bullish MACD crossover, sell on bearish"
  enabled: false
  parameters:
    fast_ema: 12
    slow_ema: 26
    signal_line: 9
    timeframe: "H1"
    min_histogram_strength: 0.0001
  risk:
    stop_loss_pips: 80
    take_profit_pips: 160
    risk_per_trade: 0.025
  symbols:
    - EURUSD
    - GBPUSD

# Stochastic Strategy
stochastic_strategy:
  name: "Stochastic Oscillator"
  description: "Buy when %K crosses above %D in oversold, sell in overbought"
  enabled: false
  parameters:
    k_period: 14
    d_period: 3
    slowing: 3
    overbought_level: 80
    oversold_level: 20
    timeframe: "M15"
  risk:
    stop_loss_pips: 45
    take_profit_pips: 90
    risk_per_trade: 0.018
  symbols:
    - AUDUSD
    - NZDUSD

# Multi-Timeframe Strategy
multi_timeframe:
  name: "Multi-Timeframe Trend Following"
  description: "Confirm trend across multiple timeframes"
  enabled: false
  parameters:
    primary_timeframe: "H1"
    confirmation_timeframe: "H4"
    trend_indicator: "sma"
    trend_period: 50
    entry_timeframe: "M15"
  risk:
    stop_loss_pips: 100
    take_profit_pips: 200
    risk_per_trade: 0.03
  symbols:
    - EURUSD
    - GBPUSD

# Breakout Strategy
breakout_strategy:
  name: "Support/Resistance Breakout"
  description: "Trade breakouts of key support/resistance levels"
  enabled: false
  parameters:
    lookback_period: 50
    min_breakout_strength: 0.0010
    timeframe: "H1"
    volume_confirmation: true
  risk:
    stop_loss_pips: 70
    take_profit_pips: 140
    risk_per_trade: 0.022
  symbols:
    - EURUSD
    - GBPUSD
    - USDJPY

# Global Strategy Settings
global_settings:
  max_concurrent_strategies: 3
  strategy_cooldown_minutes: 30  # Wait time between signals from same strategy
  correlation_filter:
    enabled: true
    max_correlation: 0.7  # Don't trade highly correlated pairs simultaneously
  
  # Time filters
  trading_hours:
    enabled: true
    start_hour: 8   # UTC
    end_hour: 18    # UTC
    exclude_weekends: true
    exclude_holidays: true
    
  # News filter
  news_filter:
    enabled: false
    high_impact_buffer_minutes: 30
    medium_impact_buffer_minutes: 15
    
  # Market condition filters
  market_filters:
    min_volatility: 0.0005
    max_volatility: 0.0050
    min_liquidity_score: 0.7
