# MT5 Python Trading Bot Configuration

# MT5 Connection Settings
mt5:
  login: ${MT5_LOGIN}
  password: ${MT5_PASSWORD}
  server: ${MT5_SERVER}
  path: ${MT5_PATH}
  timeout: 10
  max_retries: 3
  retry_delay: 5

# Trading Settings
trading:
  enabled: ${TRADING_ENABLED:true}
  mode: ${TRADING_MODE:live}  # live, paper, backtest
  max_risk_per_trade: ${MAX_RISK_PER_TRADE:0.02}
  max_daily_loss: ${MAX_DAILY_LOSS:0.05}
  max_open_positions: ${MAX_OPEN_POSITIONS:5}
  default_lot_size: 0.1
  min_lot_size: 0.01
  max_lot_size: 10.0
  
# Risk Management
risk:
  stop_loss_pips: ${STOP_LOSS_PIPS:50}
  take_profit_pips: ${TAKE_PROFIT_PIPS:100}
  trailing_stop:
    enabled: ${TRAILING_STOP_ENABLED:true}
    distance: ${TRAILING_STOP_DISTANCE:30}
    step: 10
  position_sizing:
    method: "fixed_risk"  # fixed_risk, fixed_lot, kelly, optimal_f
    risk_per_trade: 0.02
  drawdown:
    max_daily: 0.05
    max_total: 0.20
    stop_trading_threshold: 0.15

# Strategy Settings
strategies:
  default: ${DEFAULT_STRATEGY:sma_crossover}
  timeframe: ${STRATEGY_TIMEFRAME:M15}
  enabled_strategies:
    - sma_crossover
    - rsi_strategy
    - bollinger_bands
  
# Symbol Configuration
symbols:
  default: ${SYMBOLS:EURUSD,GBPUSD,USDJPY,AUDUSD}
  forex_majors:
    - EURUSD
    - GBPUSD
    - USDJPY
    - USDCHF
    - AUDUSD
    - USDCAD
    - NZDUSD
  forex_minors:
    - EURJPY
    - GBPJPY
    - EURGBP
    - AUDCAD
    - AUDNZD
    - CADJPY

# Database Settings
database:
  path: ${DATABASE_PATH:data/trading_bot.db}
  backup:
    enabled: ${BACKUP_ENABLED:true}
    interval_hours: ${BACKUP_INTERVAL_HOURS:24}
    max_backups: 7
  
# Logging Configuration
logging:
  level: ${LOG_LEVEL:INFO}
  file_path: ${LOG_FILE_PATH:logs/trading_bot.log}
  max_size_mb: ${LOG_MAX_SIZE_MB:100}
  backup_count: ${LOG_BACKUP_COUNT:5}
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  console_output: true
  
# Data Settings
data:
  cache_size: ${DATA_CACHE_SIZE:1000}
  historical_data:
    default_period: 1000
    max_period: 10000
  real_time:
    update_interval: 1
    buffer_size: 100
    
# Performance Settings
performance:
  heartbeat_interval: ${HEARTBEAT_INTERVAL:30}
  connection_timeout: ${CONNECTION_TIMEOUT:10}
  max_workers: 4
  
# Monitoring and Alerts
monitoring:
  enabled: true
  performance_tracking: true
  alerts:
    email:
      enabled: ${ENABLE_EMAIL_ALERTS:false}
      smtp_server: ${EMAIL_SMTP_SERVER}
      smtp_port: ${EMAIL_SMTP_PORT:587}
      username: ${EMAIL_USERNAME}
      password: ${EMAIL_PASSWORD}
      to_address: ${ALERT_EMAIL}
    conditions:
      - type: "drawdown"
        threshold: 0.05
      - type: "profit"
        threshold: 0.10
      - type: "connection_lost"
        
# Development Settings
development:
  debug_mode: ${DEBUG_MODE:false}
  backtesting_mode: ${BACKTESTING_MODE:false}
  paper_trading: ${PAPER_TRADING:true}
  test_data_path: "tests/fixtures/"
